/*css 初始化 */
html, body, ul, li, ol, dl, dd, dt, p, h1, h2, h3, h4, h5, h6, form, fieldset, legend, img {
    margin: 0;
    padding: 0;
}

/*将标签原有的默认内外边距去掉*/

fieldset, img, input, button {
    border: none; /*去掉边框*/
    padding: 0;
    margin: 0;
    outline-style: none; /*去掉环绕边框*/
}

ul, ol {
    list-style: none; /*去掉原样式中的小黑点*/
    /*ctrl+alt+l*/
}

input {
    padding-top: 0;
    padding-bottom: 0;

}

select, input {
    vertical-align: middle; /*输入字居中显示*/
}

select, input, textarea {
    font-size: 14px;
    margin: 0;
}

/**/
textarea {
    resize: none; /*防止拖动*/
}

img {
    border: 0;
    vertical-align: middle; /*  去掉图片底部默认的3像素空白缝隙*/
}

table {
    border-collapse: collapse; /*合并外边线*/
}

body {
    font-family:  Microsoft YaHei, <PERSON><PERSON>, "\5b8b\4f53";
}

.clearfix:before, .clearfix:after {
    content: "";
    display: table;
}

.clearfix:after {
    clear: both;
}

.clearfix {
    *zoom: 1; /*IE/7/6*/
    /*兼容IE6下的写法*/
}

h1, h2, h3, h4, h5, h6 {
    text-decoration: none; /**/
    font-weight: normal; /*不加粗*/
    font-size: 100%;
}
a{
    text-decoration: none;
}
s, i, em {
    /*一般起装饰作用*/
    font-style: normal; /*将字体变成正常字体*/
    text-decoration: none; /*去掉中划线*/
}
.fl{
    float: left;
}
.fr{
    float: right;
}