var disProvince1 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'110000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince2 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'120000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince3 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'130000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince4 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'150000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince5 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'640000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince6 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'610000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince7 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'620000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince8 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'410000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince9 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'370000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince10 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'320000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince11 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'340000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var disProvince12 = new AMap.DistrictLayer.Province({
    zIndex:14,
    adcode:'210000',
    deep:2,
    styles:{
        polygon:{
            fillColor:'#122442'
        },
        polyline:{
            strokeColor:function(properties){
                var type = properties.type;
                switch(type){
                    case 'Province_Border_China':return '#406795'//中国省界
                    case 'Province_Border_Foreign': return 'cornflowerblue';//外国省界

                    case 'City_Border_China': return '#122442';//中国地级市边界
                    case 'County_Border_China': return '#122442';//中国区县边界
                }

            }
        }
    }
});
var map = new AMap.Map('container', {
    resizeEnable: true,
    zoom:6,
    zooms:[6,18],
    center: [112.47485,37.789585]
});
map.setMapStyle('amap://styles/87fc86ed648cae752d097a75f4744133');
map.add(disProvince1);
map.add(disProvince2);
map.add(disProvince3);
map.add(disProvince4);
map.add(disProvince5);
map.add(disProvince6);
map.add(disProvince7);
map.add(disProvince8);
map.add(disProvince9);
map.add(disProvince10);
map.add(disProvince11);
map.add(disProvince12);
var trafficLayer = new AMap.TileLayer.Traffic({
    zIndex: 10
});
trafficLayer.setMap(map);