@charset "utf-8";
/*样式参考*/
a {
    text-decoration: none;
}

#page {
    height: 50px;
    display: table;
    margin: 0 auto;
}

.page {
    text-align: center;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.pagingUl {
    float: right;
    padding: 0;
    margin: 0;
    list-style: none;
    height: 30px;
}

.pagingUl li {
    float: left;
    width: 30px;
    height: 30px;
    background: #fff;
    text-align: center;
    margin-right: 5px;
    border-radius: 5px
}

.pagingUl li a {
    display: inline-block;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 28px;
    color: #999;
    font-size: 12px;
    border-radius: 5px
}

.prv,
.next {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    background: #fff;
    padding: 0 5px;
    border-radius: 5px;
}

.prv {
    margin-right: 10px;
}

.first,
.last {
    width: 38px;
    height: 30px;
    font-size: 12px;
    text-align: center;
    line-height: 30px;
    /*border: 1px solid #ddd;*/
    cursor: pointer;
    background: #fff;
    padding: 0 5px;
    border-radius: 5px;
    margin-right: 10px;
    color: #666666;
}

.prv:hover,
.next:hover,
.first:hover,
.last:hover,
.pagingUl li a:hover,
.activP {
    background-color: #126ae6!important;
    color: #fff!important;
}
