@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */
 
/* common start */
body { _margin:0; _height:100%; /*IE6 BUG*/ }
.aui_outer { text-align:left; }
table.aui_border, table.aui_dialog { border:0; margin:0; border-collapse:collapse; width:auto; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_c, .aui_e, .aui_sw, .aui_s, .aui_se, .aui_header, .aui_tdIcon, .aui_main, .aui_footer { padding:0; }
.aui_header, .aui_buttons button { font: 12px/1.11 'Microsoft Yahei', Tahoma, Arial, Helvetica, STHeiti; _font-family:Tahoma,Arial,Helvetica,STHeiti; -o-font-family: Tahoma, Arial; }
.aui_title { overflow:hidden; text-overflow: ellipsis; }
.aui_state_noTitle .aui_title { display:none; }
.aui_close { display:block; position:absolute; text-decoration:none; outline:none; _cursor:pointer; }
.aui_close:hover { text-decoration:none; }
.aui_main { text-align:center; min-width:9em; min-width:0\9/*IE8 BUG*/; }
.aui_content { display:inline-block; *zoom:1; *display:inline; text-align:left; border:none 0; }
.aui_content.aui_state_full { display:block; width:100%; margin:0; padding:0!important; height:100%; }
.aui_loading { width:96px; height:32px; text-align:left; text-indent:-999em; overflow:hidden; background:url(icons/loading.gif) no-repeat center center; }
.aui_icon { vertical-align: middle; }
.aui_icon div { width:48px; height:48px; margin:10px 0 10px 10px; background-position: center center; background-repeat:no-repeat; }
.aui_buttons { padding:8px; text-align:right; white-space:nowrap; }
.aui_buttons button { margin-left:15px; padding: 6px 8px; cursor: pointer; display: inline-block; text-align: center; line-height: 1; *padding:4px 10px; *height:2em; letter-spacing:2px; font-family: Tahoma, Arial/9!important; width:auto; overflow:visible; *width:1; color: #333; border: solid 1px #999; border-radius: 5px; background: #DDD; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFF', endColorstr='#DDDDDD'); background: linear-gradient(top, #FFF, #DDD); background: -moz-linear-gradient(top, #FFF, #DDD); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#FFF), to(#DDD)); text-shadow: 0px 1px 1px rgba(255, 255, 255, 1); box-shadow: 0 1px 0 rgba(255, 255, 255, .7),  0 -1px 0 rgba(0, 0, 0, .09); -moz-transition:-moz-box-shadow linear .2s; -webkit-transition: -webkit-box-shadow linear .2s; transition: box-shadow linear .2s; }
.aui_buttons button::-moz-focus-inner{ border:0; padding:0; margin:0; }
.aui_buttons button:focus { outline:none 0; border-color:#426DC9; box-shadow:0 0 8px rgba(66, 109, 201, .9); }
.aui_buttons button:hover { color:#000; border-color:#666; }
.aui_buttons button:active { border-color:#666; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DDDDDD', endColorstr='#FFFFFF'); background: linear-gradient(top, #DDD, #FFF); background: -moz-linear-gradient(top, #DDD, #FFF); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#DDD), to(#FFF)); box-shadow:inset 0 1px 5px rgba(66, 109, 201, .9), inset 0 1px 1em rgba(0, 0, 0, .3); }
.aui_buttons button[disabled] { cursor:default; color:#666; background:#DDD; border: solid 1px #999; filter:alpha(opacity=50); opacity:.5; box-shadow:none; }
button.aui_state_highlight { color: #FFF; border: solid 1px #1c6a9e; background: #2288cc; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee', endColorstr='#2288cc'); background: linear-gradient(top, #33bbee, #2288cc); background: -moz-linear-gradient(top, #33bbee, #2288cc); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#33bbee), to(#2288cc)); text-shadow: -1px -1px 1px #1c6a9e; }
button.aui_state_highlight:hover { color:#FFF; border-color:#0F3A56; }
button.aui_state_highlight:active { border-color:#1c6a9e; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee', endColorstr='#2288cc'); background: linear-gradient(top, #33bbee, #2288cc); background: -moz-linear-gradient(top, #33bbee, #2288cc); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#33bbee), to(#2288cc)); }
/* common end */

.aui_inner { background:rgba(0, 0, 0, .7); }
.aui_dialog { background:#FFF; border-radius:3px; }
.aui_outer { border:1px solid #000; border-radius:5px; box-shadow: 0 3px 0 rgba(0,0,0,0.1); -moz-transition:-moz-box-shadow linear .2s; -webkit-transition: -webkit-box-shadow linear .2s; transition: -webkit-box-shadow linear .2s; }
.aui_state_lock .aui_outer { box-shadow:0 3px 26px rgba(0, 0, 0, .9); }
.aui_outer:active { box-shadow:none!important; }
.aui_state_drag .aui_outer { box-shadow:none!important; }
.aui_border { border-radius:3px; }
.aui_nw, .aui_ne { width:5px; height:37px; }
.aui_sw, .aui_se { width:5px; height:5px; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_e, .aui_sw, .aui_s, .aui_se { background:rgba(0, 0, 0, .7); background:#000\9!important; filter:alpha(opacity=70); }
.aui_titleBar { width:100%; height:0; position:relative; bottom:33px; _bottom:0; _margin-top:-33px; }
.aui_title { height:27px; line-height:27px; padding:0 16px 0 5px; color:#FFF; font-weight:700; text-shadow:0 1px 0 #000; }
.aui_close { padding:0; top:2px; right:5px; width:21px; height:21px; line-height:21px; font-size:18px; text-align:center; color:#EBEBEB; font-family: Helvetica, STHeiti; _font-family: Tahoma, '\u9ed1\u4f53', 'Book Antiqua', Palatino; border:1px solid transparent; _border:0 none; background:#000; border-radius:15px;  }
.aui_state_drag .aui_close { color:#FFF; }
.aui_close:hover { background:#C72015; border:1px solid #000; _border:0 none; box-shadow: 0 1px 0 rgba(255, 255, 255, .3), inset 0 1px 0 rgba(255, 255, 255, .3); }
.aui_close:active { box-shadow: none; }
.aui_state_noTitle { }
.aui_content { color:#666; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_ne { height:5px; }
.aui_state_noTitle .aui_titleBar { bottom:0; _bottom:0; _margin-top:0; }
.aui_state_noTitle .aui_close { top:5px; }
