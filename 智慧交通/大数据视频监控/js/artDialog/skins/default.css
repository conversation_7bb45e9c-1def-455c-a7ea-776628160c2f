@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */
 
/* common start */
body { _margin:0; _height:100%; /*IE6 BUG*/ }
.aui_outer { text-align:left; }
table.aui_border, table.aui_dialog { border:0; margin:0; border-collapse:collapse; width:auto; }
.aui_c, .aui_header, .aui_tdIcon, .aui_main, .aui_footer {padding:0;}
.aui_nw, .aui_n, .aui_ne, .aui_w,.aui_e, .aui_sw, .aui_s, .aui_se{display: none}
.aui_header, .aui_buttons button { font: 12px/1.11 'Microsoft Yahei', Tahoma, Arial, Helvetica, STHeiti; _font-family:Tahoma,Arial,Helvetica,STHeiti; -o-font-family: Tahoma, Arial; }
.aui_title { overflow:hidden; text-overflow: ellipsis; }
.aui_state_noTitle .aui_title { display:none; }
.aui_close { display:block; position:absolute; text-decoration:none !important;  _cursor:pointer;border: none !important; }
/*.aui_close:hover { text-decoration:none; }*/
.aui_main { text-align:center; min-width:9em; min-width:0\9/*IE8 BUG*/; }
.aui_content { display:inline-block; *zoom:1; *display:inline; text-align:left; border:none 0; }
.aui_content.aui_state_full { display:block; width:100%; margin:0; padding:0!important; height:100%; }
.aui_loading { width:96px; height:32px; text-align:left; text-indent:-999em; overflow:hidden; background:url(icons/loading.gif) no-repeat center center; }
.aui_icon { vertical-align: middle; }
.aui_icon div { width:48px; height:48px; margin:10px 0 10px 10px; background-position: center center; background-repeat:no-repeat; }
.aui_buttons { padding:8px; text-align:right; white-space:nowrap; }
.aui_buttons button { margin-left:15px; padding: 6px 8px; cursor: pointer; display: inline-block; text-align: center; line-height: 1; *padding:4px 10px; *height:2em; letter-spacing:2px; font-family: Tahoma, Arial/9!important; width:auto; overflow:visible; *width:1; color: #333; border: solid 1px #999; border-radius: 5px; background: #DDD; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFF', endColorstr='#DDDDDD'); background: linear-gradient(top, #FFF, #DDD); background: -moz-linear-gradient(top, #FFF, #DDD); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#FFF), to(#DDD)); text-shadow: 0px 1px 1px rgba(255, 255, 255, 1); box-shadow: 0 1px 0 rgba(255, 255, 255, .7),  0 -1px 0 rgba(0, 0, 0, .09); -moz-transition:-moz-box-shadow linear .2s; -webkit-transition: -webkit-box-shadow linear .2s; transition: box-shadow linear .2s; }
.aui_buttons button::-moz-focus-inner{ border:0; padding:0; margin:0; }
.aui_buttons button:focus { outline:none 0; border-color:#426DC9; box-shadow:0 0 8px rgba(66, 109, 201, .9); }
.aui_buttons button:hover { color:#000; border-color:#666; }
.aui_buttons button:active { border-color:#666; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DDDDDD', endColorstr='#FFFFFF'); background: linear-gradient(top, #DDD, #FFF); background: -moz-linear-gradient(top, #DDD, #FFF); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#DDD), to(#FFF)); box-shadow:inset 0 1px 5px rgba(66, 109, 201, .9), inset 0 1px 1em rgba(0, 0, 0, .3); }
.aui_buttons button[disabled] { cursor:default; color:#666; background:#DDD; border: solid 1px #999; filter:alpha(opacity=50); opacity:.5; box-shadow:none; }
button.aui_state_highlight { color: #FFF; border: solid 1px #1c6a9e; background: #2288cc; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee', endColorstr='#2288cc'); background: linear-gradient(top, #33bbee, #2288cc); background: -moz-linear-gradient(top, #33bbee, #2288cc); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#33bbee), to(#2288cc)); text-shadow: -1px -1px 1px #1c6a9e; }
button.aui_state_highlight:hover { color:#FFF; border-color:#0F3A56; }
button.aui_state_highlight:active { border-color:#1c6a9e; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee', endColorstr='#2288cc'); background: linear-gradient(top, #33bbee, #2288cc); background: -moz-linear-gradient(top, #33bbee, #2288cc); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#33bbee), to(#2288cc)); }
/* common end */

.aui_inner { background:#FFF; }
.aui_border { box-shadow: inset 0 0 1px rgba(255, 255, 255, .9); }
.aui_nw, .aui_ne, .aui_sw, .aui_se { width:8px; height:8px; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_e, .aui_sw, .aui_s, .aui_se { background:rgba(0, 0, 0, .4); background:#000\9!important; filter:alpha(opacity=40); }
.aui_state_lock .aui_nw, .aui_state_lock .aui_n, .aui_state_lock .aui_ne, .aui_state_lock .aui_w, .aui_state_lock .aui_e, .aui_state_lock .aui_sw, .aui_state_lock .aui_s, .aui_state_lock .aui_se { background:rgba(0, 0, 0, .5); background:#000\9!important; filter:alpha(opacity=50); }
.aui_state_focus .aui_dialog { box-shadow: 0 0 3px rgba(0, 0, 0, 0.4); }
.aui_state_focus .aui_outer { box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1); }
.aui_state_lock .aui_border { box-shadow:0 3px 26px rgba(0, 0, 0, .9); }
.aui_state_drag .aui_outer, .aui_outer:active { box-shadow:none; }
.aui_titleBar { position:relative; height:100%; }
.aui_title { height:36px; line-height:36px;font-size: 14px; padding:0 28px 0 10px;  background-color:#3c8dbc !important; font-weight:bold;    background: -moz-linear-gradient(top, #edf5f8, #bdc6cd);text-align: left;color: #fff !important; }
.aui_state_focus .aui_title { color:#4c5a5f; }
.aui_state_drag .aui_title { background: linear-gradient(top, #bdc6cd, #edf5f8); background: -moz-linear-gradient(top, #bdc6cd, #edf5f8); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#bdc6cd), to(#edf5f8)); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#bdc6cd', endColorstr='#edf5f8'); box-shadow:none; }
.aui_state_drag .aui_titleBar { box-shadow:none; }
.aui_close { padding:0; top:4px; right:4px; width:21px; height:21px; line-height:21px; font-size:18px; color:#68767b; text-align:center; }
.aui_content { color:#666; }
.aui_state_focus .aui_content { color:#000; }
.aui_buttons { background-color:#F6F6F6; border-top:solid 1px #DADEE5; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_se { width:3px; height:3px; }
.aui_state_noTitle .aui_inner { border:1px solid #666; background:#FFF; }
.aui_state_noTitle .aui_outer { border:none 0; box-shadow:none; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_n, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_w, .aui_state_noTitle .aui_e, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_s, .aui_state_noTitle .aui_se { background:rgba(0, 0, 0, .05); background:#000\9!important; filter:alpha(opacity=5)!important; }
.aui_state_noTitle .aui_titleBar { bottom:0; _bottom:0; _margin-top:0; }
.aui_state_noTitle .aui_dialog { box-shadow: none; }