/*table样式*/
#table td,#table th,#table1 td,#table1 td,#table1 th,#table1 td,#table2 td,#table2 th,#table2 td{color: #fff !important;border:none !important;padding: 3px 8px !important;vertical-align: middle;font-size: 14px !important;}
#table,#table1,#table2{border: none !important;}
#table thead tr,#table1 thead tr,#table2 thead tr{background-image: -webkit-linear-gradient(left,rgba(96,133,255,0.6),rgba(48,69,126,0.6)) !important;}
.fixed-table-container thead th .th-inner, .fixed-table-container tbody td .th-inner{padding: 0 8px !important;}
.table-striped>tbody>tr{background-color: rgba(96,178,255,0.2) !important;font-size: 14px}
.table-striped>tbody>tr td{background-color: rgba(96,178,255,0.2) !important;font-size: 14px}
.table-striped>tbody>tr:nth-of-type(odd){font-size: 10px;background-color: rgba(96,178,255,0) !important;}
.table-striped>tbody>tr:hover{background-color: rgba(156,207,255,0.1)}
.dataAllBorder20 td{padding: 0 !important;font-size: 16px;line-height: 23px !important;margin: 0 !important;}
.dataAllBorder20 .table{margin-bottom: 10px !important}
.dataAllBorder02 .fixed-table-pagination div.pagination .pagination{     border: 1px #637ef5 solid;}
.dataAllBorder02 .fixed-table-pagination .pagination a{color:#637ef5;}
.dataAllBorder02 .fixed-table-pagination .pagination a{background-color: rgba(225,225,225,0);border-color: rgba(225,225,225,0)}
.dataAllBorder02  .pagination>.active>a, .pagination>.active>a:focus, .pagination>.active>a:hover, .pagination>.active>span, .pagination>.active>span:focus, .pagination>.active>span:hover{
    z-index: 2;
    color: #fff;
    cursor: default;
    background-color: #637ef5;
    border-color: #637ef5;
}
html{
    overflow: hidden;
}

.left_cage{
    width: 22%;
    height: 100%;
    margin-left: 0.3%;
    float: left;
}
.center_cage{
    width: 55.1%;
    height: 100%;
    margin-left: 0.3%;
    float: left;
}
.right_cage{
    width: 22%;
    height: 100%;
    margin-left: 0.3%;
    float: right;
}
.center_cage{

}
.right_cage{

}
.cage_cl{
    background-color: rgba(2,8,23,0.1);
}
.video_cage{
    padding: 4px;
    width: 100%;
    object-fit: fill;
}
.video {
    height: 100%;
    width: 100%;
}
.video_around{
    width: 49.5%;
    height: 49.5%;
    float: left;
    object-fit: fill;
}
.video_around:hover{
    border: #8bff62 2px solid;
}
.video_around_chose{
    border: #8bff62 2px solid;
}
.over_hide{
    overflow: hidden;
}
.message_scroll{
    border: rgba(12,122,200,0.5) 1px solid;
    background-color: rgba(20,66,125,0.12);
    height: 90px;
    cursor: pointer;
    margin-bottom: 6px;
}
.scroll_top{
    height: 25px;
}
.scroll_title{
    float: left;
    background-image: url("../img/pushmessage_class.png");
    background-repeat: no-repeat;
    width: 150px;
    line-height: 25px;
    color: white;
    font-size: 14px;
    text-align: center;
}
.scroll_level{
    float: left;
    background-repeat: no-repeat;
    line-height: 25px;
    width: 56px;
    background-position-y:3px;
    color: white;
    font-size: 12px;
    text-align: center;
    margin-left: 8px;
}
.scroll_level01{
    background-image:url("../img/pushmessage_level01.png");
}
.scroll_level02{
    background-image:url("../img/pushmessage_level02.png");
}
.scroll_level03{
    background-image:url("../img/pushmessage_level03.png");
}
.localize{
    display: block;
    line-height: 25px;
    margin-left: 8px;
    background-image: url("../img/pushmessage_localize_01.png");
    background-repeat: no-repeat;
    background-position-y:3px;
    width: 14px;
    height: 25px;
    float: left;
}
.localize:hover{
    background-image: url("../img/pushmessage_localize_02.png");
}
.scroll_timer{
    color: #4a97da;
    font-size: 12px;
    line-height: 25px;
    text-align: right;
    display: block;
    float: right;
    margin-right: 5px;
}
.msg_cage{
    padding-left: 10px;
    padding-right: 6px;
    height: 18px;
    overflow: hidden;
    margin-top: 8px;
}
.localize_title{
    color: #2c85d2;
}
.localize_msg{
    font-size: 14px;
    color: white;
}
.scroll_tool_outbox{
    height: 30px;
    width: 100%;
    position: absolute;
    bottom: 0;
    padding-left: 3px;
    padding-right: 3px;
    padding-bottom: 2px;
    border-radius:0px 0px 15px 15px;
    overflow: hidden;
    opacity: 0;
}
.scroll_tool_outbox_current{
    opacity: 1;
}
.scroll_tool_box{
    height: 100%;
    width: 100%;
    background-color: rgba(6,10,19,0.9);
}
.scroll_tool{
    color: #28cfa2;
    border: #28cfa2 1px solid;
    line-height: 20px;
    width: 90px;
    text-align: center;
    display: block;
    float: right;
    border-radius: 3px;
    font-size: 12px;
    margin-top: 3px;
    margin-right: 8px;
    text-decoration: none;
}

.map_title_box{
    height: 11%;
    width: 100%;
    top: 0px;
    left: 0px;
    background-color: rgba(17,25,69,0.1);
    border-radius: 11px 11px 0px 0px;
    position: relative;
}
.map_title_innerbox{
    position: absolute;
    top: -4px;
    width: 100%;
}
.map_title{
    width: 358px;
    background-image: url("../img/first_title.png");
    background-repeat: no-repeat;
    margin:auto;
    height: 28px;
    text-align: center;
    color: white;
    font-size: 14px;
    font-family: "Microsoft YaHei";
    font-weight: bold;
}
.map{
    width: 100%;
    height: 95%;
    background-repeat: no-repeat;
    background-position-x:-300px;
    background-position-y:-200px;
    border-radius: 0px 0px 8px 8px;
    position: relative;
    overflow: hidden;
    cursor:move;
}
/*.map2D{*/
    /*background-image: url("../images/2Dmap.png");*/
/*}*/
.display_box{
    width: 20%;
    height: 80%;
    border: rgba(1,57,144,0.9) 2px solid ;
    background-color: rgba(11,39,63,0.9);
    position: absolute;
    top: 6%;
    right: 0%;
}
.display_type_center_box{
    width: 100%;
    height: 94%;
    overflow: scroll;
}
.display_type_inner_box{
    margin-left: 10px;
    margin-top: 6px;
    margin-bottom: 0px;
}
.display_type_msg{
    color: white;
    font-size: 12px;
    margin-bottom: 3px;
}
.city_chose_box .display_type_center_box ul li{
    width: 50%;
    float: left;
    cursor: pointer;
}
.city_chose_box .display_type_center_box ul li:hover{
    color: #72e3ff;
}
.display_type_chose{
    width: 11px;
    height: 11px;
    display: block;
    float: left;
    margin-top: 2px;
    margin-right: 6px;
    background-image: url("../img/chose_btn.png");
}
.display_type_chose_recent{
    background-position-x: -11px;
}
.display_type_funct_box{
    width: 100%;
    height: 6.4%;
}
.display_type_funct{
    width: 100%;
    height: 100%;
    float: left;
    color: #22d8ff;
    cursor: pointer;
    text-align: center;
    font-size: 12px;
    padding-top: 4px;
    background-color: #005c70;
}
.display_type_funct:hover{
    background-color: #00768f;
    color: white;
}
.trajectory_box{
    height: 26px;
    padding-left:1px;
    padding-right: 1px;
    position: absolute;
    bottom: 13%;
    left: 22%;
    background-color: white;
}
.trajectory_box input,.trajectory_box select,.trajectory_box button{
    float: left;margin: 1px 4px 0 0;
}
.trajectory_box button{
    margin-right: 0 !important;
}
.trajectory{
    width:106px;
    line-height:20px;
    font-size: 12px;
    text-indent: 0.5em;
}
.ratio {
    display: inline-block;
    height: 24px;
    font-size: 12px;
}
.playback{
    line-height: 20px;
    font-size: 12px;
    width: 34px;
}
.map_change_box{
    display: block;
    position: absolute;
    left: 1%;
    bottom: 10%;
    height: 38px;
}
.map_change{
    display: inline-block;
    cursor: pointer;
    width: 80px;
    height: 38px;
    box-shadow: black 0px 0px 3px;
    background-repeat: no-repeat;
    margin-right: 4px;
}
.map_change:hover,.map_change_chose{
    box-shadow: #0078ff 0px 0px 3px;
    border: #0078ff 1px solid;
}
.map_change_chose{
    float: left;
}
.VM_map{
    background-image: url("../images/Vector_map.png");
    background-position-x: -53px;
    background-position-y: -20px;
}
.SCI_map{
    background-image: url("../images/Satellite_cloud_map.png");
    background-position: center;
}
.map_tool_outbox{
    width: 100%;
    background-image: url("../img/map_tool_bg.png");
    height:50px;
    cursor: auto;
    background-repeat: repeat-x;
    position: absolute;
    bottom: 0px;
    border-radius: 0px 0px 5px 5px;
}
.map_tool{
    display: block;
    width: 40px;
    height: 100%;
    margin-left: 8px;
    cursor: pointer;
    float: left;
}
.map_tool:hover{
    background-image:url("../img/map_tool_re.png");
    background-repeat: repeat-x;
}
.map_tool_inner{
    display: block;
    width: 36px; height: 100%;
    background-image: url("../img/tool.png");
    background-repeat: no-repeat;
    margin-left: 2px;
    background-position-y: 7px;
}

/*右侧*/
.analysis{
    width: 100%;
    height: 20%;
    padding-top: 2%;
    text-align: left;
    text-indent: 0.7em;
    font-size: 16px;
    font-weight: bold;
    color: white;
}
.data_show_box{
    height: 42%;
    width: 76%;
    margin: auto;
    text-decoration: none;
}
.data_cage{
    display: block;
    background-image: url("../img/data_bg.png");
    height: 100%;
    width: 16%;
    float: left;
    margin-left: 1px;
    color: white;
    text-align: center;
    font-size: 36px;
    font-family: data_Number;
    background-repeat: no-repeat;
    background-size: 100%;
}
.funct_number{
    color: #dbe9f1;
}
@font-face {
    font-family: data_Number; src: url("../fonts/data_Number.ttf");
}
.depart_number_box{
    height: 28%;
}
.depart_number_cage{
    width: 50%;
    float: left;
    height: 24px;
    padding-left: 4%;
    margin-top: 1%;
    margin-bottom: 8px;
}
.depart_number_cage li{
    display:block;
    float: left;
    line-height: 24px
}
.depart_name{
    font-size: 14px;
    color: white;
    cursor: pointer;
}
.depart_number{
    font-family: data_Number;
    font-size: 24px;
    color: #10a4db;
}
.location_msg_box{
    height: 100%;
}
.location_msg_box li{
    float: left;
    color: #cdd6db;
    font-size: 12px;
    width: 50%;
    padding-left: 3%;
    margin-bottom: 1px;
    padding-top: 2%;
    padding-bottom: 2%;
}
.location_msg01{
    background-color: rgba(0,0,0,0.1);
}
.location_msg02{
    background-color: rgba(0,0,0,0.2);
}
.location_single{
    height: 16%;
}
.location_double{
    height: 26%;
}

.danger_contain_box{
    width: 100%;
    height: 38%;
    padding-left: 10px;
}
.danger_depart_box{
    height: 40%;
}
.danger_depart{
    float: left;
    width: 50%;
    height: 50%;
    margin-bottom: 0px;
    padding-left: 4%;
}
.danger_depart01{
    /*background-image: url("../img/leftbg01.png");*/
    /*background-repeat: no-repeat;*/
    /*background-position: center;*/
    /*background-size: 78%;*/
    /*background-position-y:54% ;*/
    padding-top: 3%;
}
.danger_ico{
    height: 100%;
    background-size: 100%;
    width: 28%;
    background-repeat: no-repeat;
    float: left;
}
.data_name{
    color: white;
    font-weight: lighter;
    font-size: 12px;
    float: left;
    width: 72%;
    text-indent: 0.1em;
    cursor: pointer;
}
.data{
    color: white;
    font-size: 12px;
    font-family: data_Number;

}
.data01{
    color: #fff000;
    float: left;
    width: 36%;
    text-indent: 0.1em;
}
.data02{
    color: #00ffd2;
    float: right;
    width: 36%;
}
.tab_msg_box{
    display: block;
    height: 11%;
    width: 30%;;
    position: absolute;
    top: 0%;
    padding-top: 0.4%;
}
.tab_msg{
    color: white;
    font-weight: lighter;
    text-indent: 1.5em;
    background-image: url("../img/i01.png");
    background-repeat: no-repeat;
    background-position: 4.1% 59.5%;
    cursor: pointer;
    float: left;
    margin-left: 10px;
}
.tab_msg_box li:active{
    color: #00b9ff;
}
.tab_msg_current{
    background-image: url("../img/i02.png");
}
/*右侧搜索选项隐藏*/
.search_plate_box{
    width: 100%;
    padding:7px 10px 7px 0px;
    /*background-color: rgba(0,0,0,0.2);*/
}
.search_separate{
    width: 50%;
    float: left;
}
.search_title{
    line-height: 22px;
    font-size: 12px;
    color: white;
    width: 40%;
    float: left;
    text-align: right;
}
.search_input_box {
    width: 60%;
    float: left;
}
.search_chose_box{
    background-color: transparent;
    color: #6eafff;
    border: #00a0cc 1px solid;
    font-size: 12px;height: 22px;
}
.search_input{
    width: 100%;
    line-height: 20px;
    background-color: rgba(11,59,72,0.4);
    color: #6eafff;
    border: #00a0cc 1px solid;
    font-size: 12px;
}
.search_btn{
    display: block;
    width: 30%;
    height: 20%;
    color: white;
    background-color: #00a0cc;
    margin-left: 70%;
    border: rgba(153,187,255,0.6) 1px solid;
    border-radius: 3px;
}
.search_btn:hover{
    background-color: #00b7e9
}
.search_sesult_box{
    height: 63%;
    overflow-y: auto;
    width: 100%;
    border-top: rgba(61,123,186,0.6) 2px solid;
    border-radius: 0px 0px 5px 5px;
    padding: 4px;
}
.search_result{
    border:rgba(61,123,186,0.6) 1px solid;
    padding-left: 6px;
    margin-bottom: 6px;
    height: 60px;
}
.search_result_add{
    color: #a1d3ff;
    font-size: 14px;
    width: 86%;
    float: left;
    line-height: 30px;
}
.danger_result{
    width: 70%;
}
.danger_level{
    width: 16%;
    text-align: center;
    float: left;
    font-size: 14px;
    line-height: 30px;
}
.level01{
    color: #7a09ff;
}
.level02{
    color: #ffd416;
}
.level03{
    color: #09ffc8;
}
.search_result_road{
    margin-top: 4px;
    color: #23f3ff;
    font-size: 12px;
    clear: left;
    line-height: 30px;
}
.search_result_car{
    margin-top: 4px;
    float: left;
    color: #23f3ff;
    font-size: 12px;
    line-height: 30px;
}
.car_personal{
    width: 55%;
}
.car_time{
    width: 45%;
}
.search_location{
    display: block;
    float: left;
    width: 12px;
    height: 16px;
    background-image: url("../img/search_location.png");
    margin-top: 7px;
    margin-right: 10px;
    cursor: pointer;
}
.search_location:hover{
    background-position-x: -12px;
}
.search_eddit{
    display: block;
    float: left;
    width: 16px;
    height: 13px;
    background-image: url("../img/search_eddit.png");
    margin-top: 9px;
    cursor: pointer;
}
.search_eddit:hover{
    background-position-x: -16px;
}
.search_guiji{
    display: block;
    float: left;
    width: 17px;
    height: 15px;
    background-image: url("../img/car_guiji.png");
    margin-top: 8px;
    cursor: pointer;
}
.search_guiji:hover{
    background-position-x: -17px;
}
.check_increase{
    height: 38%;
}
.check_increase_act{
    height: 70.6%;
}
/*翻页样式*/
*{ margin:0; padding:0; list-style:none;}
a{ text-decoration:none;}
a:hover{ text-decoration:none;}
.tcdPageCode,.chemistry_tcdPageCode,.enterprise_tcdPageCode,.car_tcdPageCode{text-align: left;color: #ccc;position: absolute;left:18px;bottom: 3px}
.tcdPageCode a,.chemistry_tcdPageCode a,.enterprise_tcdPageCode a,.car_tcdPageCode a{display: inline-block;color: #428bca;height: 25px;	line-height: 25px;	padding: 0 10px;border: 1px solid #428bca;	margin: 0 2px;border-radius: 4px;vertical-align: middle;}
.tcdPageCode a:hover,.chemistry_tcdPageCode a:hover,.enterprise_tcdPageCode a:hover,.car_tcdPageCode a:hover{text-decoration: none;border: 1px solid #54afff;}
.tcdPageCode span.current,.chemistry_tcdPageCode span.current,.enterprise_tcdPageCode span.current,.car_tcdPageCode span.current{display: inline-block;height: 25px;line-height: 25px;padding: 0 10px;margin: 0 2px;color: #fff;background-color: #428bca;	border: 1px solid #428bca;border-radius: 4px;vertical-align: middle;}
.tcdPageCode span.disabled,.chemistry_tcdPageCode span.disabled,.enterprise_tcdPageCode span.disabled,.car_tcdPageCode span.disabled{	display: inline-block;height: 25px;line-height: 25px;padding: 0 10px;margin: 0 2px;	color: #bfbfbf;background: rgba(66,139,202,0.4);border: 1px solid #428bca;border-radius: 4px;vertical-align: middle;}



.total_chose_box{
    height: 60px;
}
.index_nav{
    /*height: 12%;*/
}
.chose_tltle{
    margin-left: 22px;
    display: block;
    float: left;
    color: white;
}
.year_chose{
    background-color: transparent;
    color: #72e3ff;
    border: #00c0ef 2px solid;
    width: 120px;
    text-indent:0.6em;
    border-radius: 5px;
    float: left;
}
.chose_text_in{
    background-color: transparent;
    color: #72e3ff;
    border: #00c0ef 2px solid;
    width: 120px;
    text-indent:0.6em;
    border-radius: 5px;
    float: left;
}
.chose_enter{
    background-color: #00c0ef;
    color: white;
    border: #00cdff 2px solid;
    width: 50px;
    border-radius: 5px;
    float: left;
    margin-left: 20px;
}

#selLayer{
    width: 200px;
    display: block;
    height: 26px;
    background-color: transparent;
    color: #0b7ff3;
    border: #0b7ff3 2px solid;
    position: absolute;
    right: 2px;
    border-radius: 5px;
    margin-top:2px;
}

















































