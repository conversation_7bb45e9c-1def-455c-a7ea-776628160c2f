/*all*/
@charset "utf-8";
/* CSS Document */
.laydate_body .laydate_bottom{height: 30px !important;}
.find_input2 .find_input2{width: 100px !important;}
*{
    margin:0;
    padding:0;
    font-family:"微软雅黑";}
::-webkit-scrollbar-track{border-radius: 10px;-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0);}/*滚动条的滑轨背景颜色*/
::-webkit-scrollbar-thumb{background-color: rgba(0,0,0,0.05);border-radius: 10px;-webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,.1);}/*滑块颜色*/
::-webkit-scrollbar-thumb{background-color: rgba(0,0,0,0.2);border-radius: 10px;-webkit-box-shadow: inset 1px 1px 0 rgba(0,0,0,.1);}
::-webkit-scrollbar{width: 16px;height: 16px;}/* 滑块整体设置*/
::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb{border-radius: 999px;border: 5px solid transparent;}
::-webkit-scrollbar-track{box-shadow: 1px 1px 5px rgba(0,0,0,.2) inset;}
::-webkit-scrollbar-thumb{min-height: 20px;background-clip: content-box;box-shadow: 0 0 0 5px rgba(0,0,0,.2) inset;}
::-webkit-scrollbar-corner{background: transparent;}/* 横向滚动条和纵向滚动条相交处尖角的颜色 */
.clear{
    clear: both;
}
.map_find{
    padding-top: 5px;
    padding-bottom: 5px;
    border-bottom: 1px solid #CCCCCC;
    font-size: 13px;
    padding-left: 10px;
}
.map_find1{
    padding-top: 10px;

    border-bottom: 1px solid #CCCCCC;
    font-size: 13px;
    padding-left: 10px;
}
.map_find label{
    margin-right: 5px;
}
.map_input{
    width: 220px;
    height: 24px;
    border-radius: 3px;
    border:1px solid #CCCCCC;
    margin-right: 10px;
}
.find_but{
    padding-left: 8px;
    padding-right: 8px;
    padding-top: 3px;
    padding-bottom: 4px;
    background-color: #0D8BBD;
    color: #ffffff;
    border:none;
    border-radius: 3px;
}
.find_but:active{
    background-color: #1c6a9e;
}
.map_con{
    width: 100%;
    height: 100%;
    background-image: url("../img/map01.png");
    position: absolute;
}
.map_tail{
    width: 600px;
    height: 350px;
    position:relative;
    margin: 0 auto;
    margin-top: 50px;
    background-color: #ffffff;
    box-shadow: 5px 5px 3px #333;
    border-radius: 5px;


}
.map_h{
    padding-top: 10px;
    padding-bottom: 5px;
    text-align: center;
    border-bottom: 1px solid #CCCCCC;
    width: 96%;
    margin-left: 2%;
}
.map_table{
    width: 96%;
    margin-left: 2%;
    border:none;
    font-size: 13px;
    color: #262626;
}
.map_mos{
    text-decoration: none;
    padding-top: 3px;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 4px;
    color: #ffffff;
    background-color: #0D8BBD;
    border:none;
    border-radius: 3px;
}
.map_mos:active{
    background-color: #00a0e9;
}
.map_video{
    text-decoration: none;
    padding-top: 3px;
    padding-left: 10px;
    padding-right: 10px;
    padding-bottom: 4px;
    color: #ffffff;
    background-color: #00aa00;
    border:none;
    border-radius: 3px;
}
.map_video:active{
    background-color: #00e765;
}
.map_p01{
    padding-top: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #CCCCCC;
    width: 96%;
    margin-left: 2%;
    font-size: 12px;
    color: #666666;
}
.map_p02{
    width: 96%;
    margin-left: 2%;
    height: 170px;
    margin-top: 5px;

}
.map_p02 img{
    width: 100%;
    height: 100%;
}
.map_close{
    width: 20px;
    height: 20px;
    position: absolute;
    right: -5px;
    top:-5px;
}
.p_but{
    padding-top: 5px;
    padding-left: 5px;
    font-size: 12px;
}
.p_but a{
    margin-right: 10px;}
#table,#table1,#table2{
    font-size: 12px !important;
    color: #475059 !important;
    /*border-top: none !important;*/
    /*margin-top: 6px !important;*/
}
#table a{
    margin-right: 10px;
}
#table th,#table1 th,#table2 th{
    border: 1px solid #ccc;


}
.add_i{
    width: 16px;
    height: 16px;
    position: relative;
    display: inline-block;
    background-image: url("../img/table_add.png");
    top:3px;
    margin-right: 2px;
}
.del_i{
    width: 16px;
    height: 16px;
    position: relative;
    display: inline-block;
    background-image: url("../img/table_del.png");
    top:3px;
    margin-right: 1px;
}
.down_i{
    width: 16px;
    height: 16px;
    position: relative;
    display: inline-block;
    background-image: url("../img/table_down.png");
    top:3px;
    margin-right: 1px;
}
.print_i{
    width: 16px;
    height: 16px;
    position: relative;
    display: inline-block;
    background-image: url("../img/table_print.png");
    top:3px;
    margin-right: 1px;
}
.div_find{
    width: 100%;
    padding-bottom: 5px;
    padding-top: 5px;
    border-bottom: solid 1px #cccccc;
    top:5px;
    font-size: 12px;
}
.find_labela {
    width: 8%;
    text-align: right;
    font-size: 12px;
    display: inline-block;
}
.find_input{
    width: 18%;
    height: 28px;
    border:solid 1px #ccc;
    border-radius: 3px;

}
.find_but{
    padding-left: 9px;
    padding-bottom: 4px;
    padding-top: 4px;
    padding-right: 8px;

    background-color:#00a0e9;
    color: #ffffff;
    border-radius: 5px;
    border:none;
    margin-right: 10px;


}
.find_but:active{
    background-color: #00a0e9;
    border: none;}
.find_but1{
    padding-left: 9px;
    padding-bottom: 4px;
    padding-top: 4px;
    padding-right: 8px;
    background-color:#2e8ded;
    color: #ffffff;
    border-radius: 5px;
    border: none;
}
.find_but1:active{
    background-color: #044588;
}
.find_span{
    float: right;
    margin-right: 15px;
    margin-top: 5px;

    font-size: 13px;
}
.find_span a{
    text-decoration: none;
    color: #333;
}
.find_span1{
    float: right;
    margin-right: 15px;
    margin-top: 5px;

    font-size: 13px;
    display: none;
}
.find_span1 a{
    text-decoration: none;
    color: #333;
}
.i_open{
    width: 14px;
    height: 14px;
    position: relative;
    display: inline-block;
    background-image: url("../img/opne.png");
    top:2px;
}
.i_close{
    width: 14px;
    height: 14px;
    position: relative;
    display: inline-block;
    background-image: url("../img/colse.png");
    top:2px;
}
.user_table{
    width: 96%;
    margin-left: 2%;
    border:none;
    font-size: 13px;
}
.user_table tr{
    height: 30px;

}
.user_td{
    text-align: right;
}
.user_td1{
    width: 100px;
}
.user_rdio{
    width: 15px;
    height: 15px;
    position: relative;
    top:3px;
    margin-right: 5px;
}
.rideo_label{
    margin-right: 10px;
    font-size: 13px;
}
.user_save{
    background-color: #00aa00;
    color: #ffffff;
    padding-bottom: 4px;
    padding-top: 3px;
    padding-left: 8px;
    padding-right: 8px;
    border:none;
    border-radius: 3px;
    margin-right: 10px;
}
.user_save:active{
    background-color: #00ca6d;
}
.user_esc{
    background-color: #d58512;
    color: #ffffff;
    padding-bottom: 4px;
    padding-top: 3px;
    padding-left: 8px;
    padding-right: 8px;
    border:none;
    border-radius: 3px;
}
.user_esc:active{
    background-color: #985f0d;
}
.rose_text{
    width: 350px;
    height: 100px;
    resize: none;
}
.part_span{
    float: right;
    margin-right: 5px;
}
.part_table{
    float: left;
    width: 79%;
    height: 600px;
    border:1px solid #0D8BBD;
    margin-left: 2px;
}
.part_tree{
    float: right;
    width: 20%;
    height: 600px;
    border:1px solid #0D8BBD;
    margin-right: 5px;
}
.part_tree ul{
    list-style: none;
    text-indent: 10px;
}
.tree_h{
    height: 40px;
    background-color: #EEEEEE;
    margin-top: 0px;
    line-height: 40px;
    text-indent: 5px;
}
.static_all{
    width: 100%;
    height: 100%;


}
.static01{
    width: 98%;
    margin-left: 1%;
    height: 300px;

    margin-top: 5px;
}
.static02{
    width: 98%;
    margin-left: 1%;
    height: 300px;

    margin-top: 5px;
}
.static03{
    width: 98%;
    margin-left: 1%;
    height: 300px;

    margin-top: 5px;
}
.alam_static{
    width: 98%;
    height: 350px;


    margin-left: 1%;
    margin-top: 5px;

}
.alam_static1{

    width: 100%;

    background-color: #ffffff;

}
.alstic_ledt{
    padding-top: 10px;
    width: 59%;
    height: 350px;
    float: left;
}
.alstic_right{
    padding-top: 10px;
    width: 39%;
    height: 350px;
    float: right;
    background-color: #ffffff;


}
.alstic_right p{

    padding-bottom: 5px;
}
.spqn_red{
    color: #f11010;
    font-size: 20px;
}
.sque_red{
    width: 16px;
    height: 16px;
    background-color: #c23531;
    display: inline-block;
    margin-right: 10px;
    position: relative;
    top:3px;
}
.span_margin{
    margin-left: 10px;
}
.sque_02{
    width: 16px;
    height: 16px;
    background-color: #d48265;
    display: inline-block;
    margin-right: 10px;
    position: relative;
    top:3px;
}
.cloum_alam{
    width: 100%;
    height: 350px;
}
.book_con01{
    width: 98%;
    border-radius: 6px;
    margin: 2% auto;
    background-color: #ffffff;
    padding-top: 2%;
    padding-bottom: 2%;
    font-size: 14px;
    box-shadow:-5px 0 5px #ccc, /*左边阴影*/
    5px 0 5px #ccc, /*右边阴影*/
    0 -5px 5px #ccc, /*顶部阴影*/
    0 5px 5px #ccc; /*底边阴影*/


}
.book_p{
    width: 94%;
    margin-left: 3%;

    margin-bottom: 15px;
    padding-bottom: 10px;
}
.book_titSpan{
    float: right;
}
.book_p label{
    color: #3665a9;
    margin-right: 5px;
}
.book_input01{
    width: 100px;
    border-top:none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #a9c9f7;
    margin-right: 5px;
    height:30px;
}
.book_input02{
    width: 60px;
    border-top:none;
    border-left: none;
    border-right: none;
    border-bottom: 1px solid #a9c9f7;
    margin-right: 5px;
    height:30px;
}
.book_input03{
    width: 97%;
    margin-left: 1%;
    height:30px;
    border:none;

}
.book_h01{
    width: 94%;
    margin-left: 3%;
    padding-top: 15px;
    margin-bottom: 10px;
    text-align: center;
    letter-spacing: 1px;
    font-size: 16px;

}
.label_red{
    margin-left: 5px;
    margin-right: 5px;
    color: #f50f35;
}
.book_table{
    width: 94%;
    margin-left: 3%;
    border-collapse:collapse;


}
.book_table tr{

    border:1px solid #ccc;
}
.book_table td{
    padding-top: 8px;
    padding-bottom: 8px;
    padding-left: 5px;

}
.input_radio{
    position: relative;
    top:1px;
    margin-right: 5px;
    margin-left: 8px;
}
.textArae{
    width: 96%;
    margin-left: 1%;
    height: 350px;
    resize: none;


}
.textArae01{
    width: 96%;
    margin-left: 1%;
    height: 350px;
    resize: none;
    border:none;


}
.label_right{
    float: right;
    margin-right: 5px;
}
.td_label{
    color: #3665a9;

    margin-right: 5px;
    padding-top: 5px;
    padding-bottom: 10px;
    text-align: right;

}
.book_input04{
    width: 20%;
    height: 30px;
    margin-left: 3%;

}
.book_input05{
    width: 45%;
    height: 30px;
    margin-left: 3%;

}
.book_foot{
    width: 100%;
    text-align: center;
    margin-top: 25px;
    margin-bottom: 25px;
}
.book_foot input{
    margin-right: 20px;
    padding-left: 5px;
    padding-right: 5px;
}
.reead{

    background-color: #EEEEEE;
}
.tr_color{
    background-color: #ECF4FB;
    text-align: center;
    font-weight: bold;
}
.P_chart{
    width: 98%;
    margin-left: 1%;
    height: 500px;

}
.p_static{
    padding-top: 15px;

    text-align: center;
    width: 100%;
    font-weight: bold;
    font-size: 18px;
}
.static_span01{
    margin-right: 15px;
    margin-left: 5px;
    font-size: 12px;
}
.static_span02{
    margin-right: 15px;
    float: right;
    font-size: 12px;
}
.static_tabel{
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}
.static_tabel td{
    padding-top: 5px;
    padding-bottom: 5px;
    border:1px solid #CCCCCC;
    text-align: center;
}
.td_red{
    color: red;
}
.td_blue{
    color: #1F547E;
}
.index_top{
    color: #ffffff;
    line-height: 60px;
}
.i_start {
    width: 8px;
    height: 8px;
    position: relative;
    display: inline-block;
    background-image: url("../img/start.png");

    margin-right: 5px;
    top:-2px;
}


*,h6,button,ul,li{padding: 0;margin: 0}

.layui-layer-title{background-color: #3c8dbc !important;font-weight:500;color:#fff !important; border:none !important;height: 36px !important;line-height: 36px !important;}
.layui-layer-rim {border: 0 solid #8D8D8D !important;border-radius: 5px;box-shadow: 0 5px 15px rgba(0,0,0,.4)!important;  }
.notice_check{padding: 16px 15px;font-size: 12px}
.notice_check p label{text-align: right;font-size: 12px;display: inline-block;width: 60px}
.find_input{width: 198px;height: 30px;border:solid 1px #ccc;border-radius: 4px;text-indent: 5px}
.notice_check .check_btn{height: 30px;border: none;margin-left: 5px;width: 65px;border-radius: 4px;background-color: #337ab7;color: #fff;padding-left: 18px;
    background-image: url(../img/1_03.png);background-repeat: no-repeat;background-position: 11px 8px}
.notice_nav ul li{float: left}
.notice_nav ul{border: 1px #ccc solid;border-radius: 2px}
.notice_nav ul li a{display:inline-block;height: 28px;text-align:center;line-height:28px;color:#101010;border: none;width: 65px;border-right:1px #ccc solid;background-color: #fff;cursor: pointer}
.notice_nav ul li:nth-child(4) a{border-right:none}
.notice_nav ul li a:hover{background-color: #f5f5f5}
.dropdown-menu {min-width: 120px !important;margin-top: 4px;font-size: 12px}
.dropdown-menu li {padding:5px 15px;cursor: pointer}
.dropdown-menu li:hover{background-color: #f5f5f6}
.fixed-table-pagination{margin-top:0 !important;margin-bottom:0 !important;position: fixed !important;bottom:10px !important;left: 0 !important;width: 100%!important;border-top:1px #ccc solid}
.pagination-detail{margin-top:0 !important;margin-bottom:0 !important;}
.pagination{margin-top:3px !important;margin-bottom:5px !important;}

.l_left{float: left}
.r_right{float: right}
.notice_bot{position: fixed;bottom: 0;background-color: #ecf0f5;padding: 5px 0 5px 0;z-index:9999;border-top:1px #ccc solid;width: 100%;}
.notice_bot>div{font-size: 12px;margin-left: 10px;color: #07141e;line-height: 28px}
.notice_bot>div input{margin-right: 2px;vertical-align: middle}
.div_form{margin:4% auto 0 auto;width: 90% }
.div_form>div{margin: 5px}
.but_p{margin-right: 10px}
.but_p button{width: 58px;height: 28px;line-height: 28px;border: none;background-color: #5cb85c;color: #fff;border-radius: 4px;cursor: pointer}
.but_p .but_close{background-color: #d9534f;margin-left: 10px}

.notice_nav a{height: 28px;line-height: 28px;padding: 0 12px;border-radius: 0;float: left;font-size: 13px}
.notice_nav a:nth-child(1){border-top-left-radius: 4px;border-bottom-left-radius: 4px}
.notice_nav a:nth-child(3){border-top-right-radius: 4px;border-bottom-right-radius: 4px}
.notice_nav span{line-height: 26px;margin-right: 2px;color: #7a869d;font-size: 12px}

/*role*/
*,p,button,ul,li{padding: 0;margin: 0}
ul{list-style: none}
.l_left{float: left}
.r_right{float: right}
.clear{clear: both}
th{background-color: #e7f0f6 !important;}
.layui-layer-title{background-color: #3c8dbc !important;font-weight:bold;color:#fff !important; border:none !important;height: 33px;line-height: 33px;}
.layui-layer-rim {border: 0px solid #8D8D8D !important;border-radius: 5px;box-shadow: 0 5px 15px rgba(0,0,0,.4)!important;  }

.notice_main{height: 98%;width: 100%;border: 1px #ccc solid;background-color: #fff;overflow: auto}
.notice_check{padding: 16px 15px;font-size: 12px}
.notice_check p label{text-align: right;font-size: 12px;display: inline-block;}
.find_input{width: 198px;height: 30px;border:solid 1px #ccc;border-radius: 4px;}
.find_input1{width: 64px;height: 30px;border:solid 1px #ccc;border-radius: 4px;}
.notice_check .check_btn{height: 30px;border: none;margin-left: 5px;width: 65px;border-radius: 4px;background-color: #337ab7;color: #fff;padding-left: 18px;
    background-image: url(../img/1_03.png);background-repeat: no-repeat;background-position: 11px 8px}
.dropdown-menu {min-width: 120px !important;margin-top: 4px;font-size: 12px}
.dropdown-menu li {padding:5px 15px;cursor: pointer}
.dropdown-menu li:hover{background-color: #f5f5f6}
.fixed-table-pagination{position: fixed !important;bottom:15px !important;left: 0 !important;width: 100%!important;border-top:1px #ccc solid}
.pagination-detail{margin-top:0 !important;margin-bottom:0 !important;}
.pagination{margin-top:3px !important;margin-bottom:5px !important;}

.notice_nav a{height: 28px;line-height: 28px;padding: 0 12px;border-radius: 0;float: left;font-size: 13px}
.notice_nav a:nth-child(1){border-top-left-radius: 4px;border-bottom-left-radius: 4px}
.notice_nav a:nth-child(3){border-top-right-radius: 4px;border-bottom-right-radius: 4px}
.notice_nav span{line-height: 26px;margin-right: 2px;color: #7a869d;font-size: 12px}

/*department*/
*,h6,button{padding: 0;margin: 0}
ul{list-style: none}
.l_left{float: left}
.r_right{float: right}
.clear{clear: both}
th{background-color: #e7f0f6 !important;}
.nav_active{;background-image: url(../img/ccc_03.png);background-repeat: no-repeat;background-position: top left;background-color: #f5f5f5}
.news_main{height: 98%;width: 100%}
.layui-layer-title{background-color: #3c8dbc !important;font-weight:bold;color:#fff !important; border:none !important;height: 33px;line-height: 33px;}
.news_main h6{font-weight: bold;padding-left: 10px;line-height: 31px;border-bottom: 1px #ccc solid}
.news_left{width: 16%;height: 100%;border: 1px #ccc solid;margin-right: 0.5%;background-color: #fff}
.news_right{width: 83.5%;height: 100%;border: 1px #ccc solid;background-color: #fff;overflow: auto}
.department_table .fixed-table-pagination{left: 16.5% !important;width: 83.5% !important;}

h6{font-weight: bold;padding-left: 10px;line-height: 31px;border-bottom: 1px #ccc solid}


.notice_check_last{padding:8px 15px 16px 15px !important}
.notice_check .find_input,.notice_check_last .find_input{margin: 0 10px 0 2px}
.notice_check_last .last_input{margin-right: 2px}

/*book01*/

.table_td{color: #3665a9;text-align: right;background-color: #f5f5f5;width: 90px}
.table_td p{display: inline-block}
.table_td span{color: red;width: 20px;display: inline-block;vertical-align:middle;text-align: center}
.book_con01 td{text-align: right;color: #475059}
.book_con01 .sex{text-align: left}
.book_con01 td:nth-child(2n):hover,.book_con01 tr:hover{background-color: #fff !important;}

/*from*/
*,a{margin: 0;padding: 0}
.from_main{height: 100%;width: 100%;overflow: auto}
ul{list-style: none}
.l_left{float: left}
.r_right{float: right}
.clear{clear: both}
*,th,td,table{padding: 0;margin: 0}
td,th{text-align: center;font-size: 12px;vertical-align: middle !important; }
#table td,#table th,#table1 td,#table1 th,#table2 td,#table2 th{color: #475059 !important;}
thead th{background-color: #ECF4FB;border: #ccc 1px solid !important;}
tbody tr:hover{background-color: #f3f3f3}

.news_check{padding: 16px 15px 16px 15px;border: 1px #ccc solid;margin-bottom: 4px;background-color: #fff;}
.check_left input{height: 28px;width: 198px;border-radius: 4px;border: 1px #ccc solid}
.check_left button{height: 28px;border: none;margin-left: 6px;width: 65px;border-radius: 4px;background-color: #337ab7;color: #fff;padding-left: 18px;
    background-image: url(../img/100_03.png);background-repeat: no-repeat;background-position: 11px 8px}
.form_h1{line-height: 30px;text-align: center;font-size: 18px;font-weight: bold}
.from_p{font-size: 12px;line-height: 24px}
.top_btn{margin-left: 10px}
.check_right span{line-height: 26px;margin-right: 2px;color: #7a869d;font-size: 12px}
.top_btn a,.check_right a{height: 28px;line-height: 28px;padding: 0 12px;border-radius: 0;float: left;font-size: 13px}
.top_btn a:nth-child(1),.check_right a:nth-child(1){border-top-left-radius: 4px;border-bottom-left-radius: 4px}
.top_btn a:nth-child(4),.check_right a:nth-child(2){border-top-right-radius: 4px;border-bottom-right-radius: 4px}
.top_btn .active{color: #333;background-color: #e6e6e6;border-color: #adadad;}

/*analysis*/
.from_main{height: 100%;width: 100%;overflow: auto}
ul{list-style: none}
.l_left{float: left}
.r_right{float: right}
.clear{clear: both}
*,th,td,table{padding: 0;margin: 0}
.check_right ul li{float: left}
.check_right ul{border: 1px #ccc solid;border-radius: 2px}
.check_right ul li button{height: 28px;border: none;width: 65px;border-right:1px #ccc solid;background-color: #fff}
.check_right ul li:nth-child(2) button{border-right:none}
.check_right ul li button:hover{background-color: #ccc}
.news_check{padding: 16px 15px 16px 15px;border: 1px #ccc solid;margin-bottom: 4px;background-color: #fff;}
.check_left input{height: 28px;width: 198px;border-radius: 4px;border: 1px #ccc solid}
.check_left button{height: 28px;border: none;margin-left: 6px;width: 65px;border-radius: 4px;background-color: #337ab7;color: #fff;padding-left: 18px;
    background-image: url(../img/1_03.png);background-repeat: no-repeat;background-position: 11px 8px}
.e_chart{margin: 10px 0}
.charts{width: 49.5%;border: 1px #ccc solid;border-radius: 4px;margin-right: 1%}
.last_charts{margin-right: 0}
.charts_last{width: 100%}
.charts p{border-bottom: 1px #ccc solid;line-height: 28px;padding: 0 10px;font-size: 12px;background-color: #e7f0f6;font-weight: 800;color:#666}
.charts span{color:#00a7d0}
.check_right span{line-height: 26px;margin-right: 2px;color: #7a869d;font-size: 12px}
.top_btn a,.check_right a{height: 28px;line-height: 28px;padding: 0 12px;border-radius: 0;float: left;font-size: 13px}
.top_btn a:nth-child(1),.check_right a:nth-child(1){border-top-left-radius: 4px;border-bottom-left-radius: 4px}
.top_btn a:nth-child(4),.check_right a:nth-child(2){border-top-right-radius: 4px;border-bottom-right-radius: 4px}
.top_btn .active{color: #333;background-color: #e6e6e6;border-color: #adadad;}

.charts_last .fixed-table-pagination{position: static !important;border-top: none !important;}

.fixed-table-container tbody .selected td {
    background-color:#bbbbbb !important;
}
.ts{margin-left: 16px}
.ts li{color: #bb8940;line-height: 24px;font-size: 12px;}
.ts li span{margin-right: 16px}



/*good页面样式*/
.good_pic label{float: left}
.good_pic .good_pic_ul{float: left}
.clear{clear: both}
.goods_main{margin: 10px 0 0 30px}
.goods_main label{line-height: 32px;font-size: 12px}
.goods_main .l_left:nth-child(2){width: 80%;height: 100px}
.goods_main .l_left textarea{width: 100%;height: 100px;border: 1px #ccc solid;border-radius: 4px;resize:none}
.good_pic_ul ul li{float: left;margin-right: 10px;padding: 10px}
.good_pic_ul ul{border: 1px #ccc solid;border-radius: 4px;margin: 4px 0 10px 0}
.good_pic_ul ul li img{width: 80px;height: 80px}
.goods_input{width: 30%;height: 32px;border-radius: 4px;border: none;border: 1px #ccc solid;margin-bottom: 10px}
.goods_input1{width: 13.5%;height: 32px;border-radius: 4px;border: none;border: 1px #ccc solid;margin:0 1% 10px 1%}
.good_btn{width: 80px;margin: 10px auto}
.good_btn button{width: 58px;height: 28px;line-height: 28px;border: none; background-color: #5cb85c;color: #fff;border-radius: 4px;  }
.pic_li_div{position: relative}
.pic_li_div .pic_btn{position: absolute;right: -2px;top: -4px;cursor: pointer}
.annyTable{
    width: 96%;
    border-collapse: collapse;
    border:1px solid #CCCCCC;
    margin:0 2% 2% 2%;
}
.annyTable td{
    padding-top: 5px;
    padding-bottom: 5px;
}
.first_td{font-weight: bold;  background-color: #eeeeee;width: 12%}
.annyTable tr:nth-child(1){
    font-weight: normal !important;  background-color: #fff !important;
}
.annyTable01{
    margin: 10px 0.5%;
    width: 99%;
    border-collapse: collapse;

}
.annyTable01 td{
    padding-bottom: 10px;
    padding-top: 10px;
}