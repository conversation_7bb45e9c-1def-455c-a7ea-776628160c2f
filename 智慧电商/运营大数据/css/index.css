/*简单初始化*/
html{
    font-size: 100px;/*设置html字体大小以便rem*/
}
html,body{
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}
ul{
    list-style: none;
    margin: 0;
    padding: 0;
}
a{
    text-decoration: none;
}
/*正文内容*/
.t_container{
    width: 100%;
    height: 100%;
    background: url('../img/bg.png') no-repeat;
    background-size: 100% 100%;
}
.t_h_bg{
    width: 100%;
    height: 80px;
    line-height: 80px;
    background: url('../img/t_header.png') no-repeat;
    background-size: 100% 100%;
    text-align: center;
}
.t_h_bg span{
    font-size: 32px;
    color: #fff;
    display: inherit;
}
.t_main{
    text-align: center;
}
.t_box{
    width: 8rem;
    height: 4rem;
    background: url('../img/t_bg.png') no-repeat;
    background-size: 100% 100%;
    display: inline-block;
    float: left;
    position: relative;
    margin-left: 1.06rem;
    margin-top: 0.31rem;
}
.t_title{
    text-align: center;
    font-size: 0.16rem;
    color: #fff;
    font-weight: bold;
    height: 0.4rem;
    line-height: 0.4rem;
    width: 100%;
    margin-top: 0.4rem;
}
.t_list{
    width: 100%;
    text-align: center;
    line-height: 0rem;
    height: 0.6rem;
}
.t_min{
    display: inline-block;
    width: 1.4rem;
    height: 0.4rem;
    line-height: 0.4rem;
    background: url('../img/t_border.png') no-repeat;
    background-size: 100% 100%;
    font-size: 0.16rem;
    color: #fff;
    position: relative;
}
.t_min i{
    position: absolute;
    display: inline-block;
    width: 0.20rem;
    height: 0.28rem;
    background: url('../img/top.png') no-repeat;
    background-size: 100% 100%;
    top: 0;
    bottom: 0;
    margin: auto;
    right: 0.05rem;
}
.t_min i.down{
    background: url('../img/down.png') no-repeat;
    background-size: 100% 100%;
}
.t_number{
    font-size: 0.24rem;
}
.main_table{
    font-size: 16px;
}
.main_table tr{
    height: 42px;
}
.main_table{
    width: 88%;
    margin-top: 25px;
    margin: 0 auto;
}
.main_table table{
    width: 100%;
}
.main_table thead tr{
    height: 42px;
}
.main_table th{
    font-size: 14px;
    font-weight: 600;
    color:#61d2f7;
    text-align: center;
}

.main_table td{
    color:#fff;
    font-size: 14px;
    text-align: center;
}
.main_table tbody tr:nth-child(1),
.main_table tbody tr:nth-child(3),
.main_table tbody tr:nth-child(5){
    width: 98%;
    background-color: #2B3AA8;
    box-shadow:-10px 0px 15px #2C58A6 inset,   /*左边阴影*/
    10px 0px 15px #2C58A6 inset;  /*右边阴影*/
}