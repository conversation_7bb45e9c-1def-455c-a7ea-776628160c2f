<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>大数据统计展示大屏</title>
		<!-- 新 Bootstrap 核心 CSS 文件 -->
		<link href="css/bootstrap.min.css" rel="stylesheet">
		<!-- jQuery文件。务必在bootstrap.min.js 之前引入 -->
		<script src="js/jquery.min.js"></script>
		<!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
		<script src="js/bootstrap.min.js"></script>
		<link href="css/index.css" rel="stylesheet" />
	</head>
	<body>
		<header>大数据统计展示大屏</header>
		<div class="container m-20">
			<div class="row">
				<div class="col-lg-3">
					<div class="box1">
						<div class="title">标题标题</div>
						<div class="box1_con" id="box1"></div>
					</div>
					<div class="box2 m-20">
						<div class="title">标题标题</div>
						<div class="box2_con" id="box2"></div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="box3">
						<div class="title">标题标题</div>
						<div class="box3_con">
							<div class="box3_con_left" id="box3_left"></div>
							<div class="box3_con_right">
								<div class="box3_con_right_top">
									<div class="row">
										<div class="col-lg-4 data_bg"><p>12569台</p><small>设备总数</small></div>
										<div class="col-lg-4 data_bg"><p>12375台</p><small>运行设备</small></div>
										<div class="col-lg-4 data_bg"><p>178台</p><small>月修设备</small></div>
									</div>
								</div>
								<div class="box3_con_right_bot" id="box3_right"></div>
							</div>
						</div>
					</div>
					<div class="box4 m-20">
						<div class="title">标题标题</div>
						<div class="box4_con" id="box4"></div>
					</div>
				</div>
				<div class="col-lg-3 box5">
					<div class="title">标题标题</div>
					<div class="box5_con">
						<div class="box5_con_top" id="box5"></div>
						<div class="title m-20">标题标题</div>
						<div class="box5_con_bot">
							<ul>
								<li>1、列表类信息标题测试列表类信息标题测试列表类信息标题测试</li>
								<li>2、列表类信息标题测试列表类信息标题测试列表闻标题测试</li>
								<li>3、列表类信息标题测试列表类信息标题测试信息标题测试</li>
								<li>4、列表类信息标题测试标题测试列表类信息标题测试</li>
								<li>5、列表类信息标题测试列表类信息标题测列表类信息标题测试</li>
								<li>6、列表类信息标题测试列表类信息标题测试列表测试</li>
								<li>7、列表类信息标题测试列表类信息标题测试列表类信息标题测试</li>
								<li>8、列表类信息标题测试列表类信息标题测试列表类标题测试</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script type="text/javascript" src="js/echarts.min.js"></script>
		<script type="text/javascript" src="js/box1.js"></script>
		<script type="text/javascript" src="js/box2.js"></script>
		<script type="text/javascript" src="js/box3_left.js"></script>
		<script type="text/javascript" src="js/box3_right.js"></script>
		<script type="text/javascript" src="js/box4.js"></script>
		<script type="text/javascript" src="js/box5.js"></script>
	</body>
</html>
