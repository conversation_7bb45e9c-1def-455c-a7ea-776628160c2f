html,body{
    padding:0;
    margin: 0;
    width:100%;
    height:100%;
    font-family: 微软雅黑;
    font-size: 14px;
}
ul,li,img{
    padding:0;
    margin: 0;
}
.BMap_cpyCtrl
{
    display:none;
}
.anchorBL{
    display:none;
}

#mapContainer{
    width:100%;
    height:100%;
}
.nav-top{
    position: fixed;
    top:0;
    left: 0;
    height: 80px;
    width: 100%;
    background-color: rgba(3,21,24,0.7);
    text-align: center;
}
.nav-top-title{
    line-height: 80px;
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;

}
.nav-top-time{
    position: absolute;
    top:0;
    right: 10px;
    padding: 20px;
}
.nav-top-time span{
    display: block;
    color:#ffffff;
    line-height: 20px;
}

.tip-container{
    background-color: rgba(22, 31, 38, 0.87);

}
.tip-title{
    width: 100%;
    height: 48px;
    color:#ffffff;
    overflow: hidden;
    box-sizing: border-box;
    border-bottom: 1px solid #052025;
}
.tip-title span{
    line-height: 48px;
    vertical-align: middle;
    font-size: 16px;
    font-weight: 600;
}
.tip-title span img{
    width: 16px;
    height: 16px;
}
.tip-title span:nth-child(1){
    float: left;
    margin-left: 12px;
}
.tip-title span:nth-child(2){
    float: left;
    margin-left: 10px;
}
.tip-title span:nth-child(3){
    float: right;
    margin-right: 10px;
}
.tip-content{
    padding: 16px;
}

.tip-content ul li {
    color:#ffffff;
    list-style: none;
    overflow: hidden;
    line-height: 30px;
}

.zhzb-li-name{
    float: left;
    /*font-weight: 600;*/
}
.zhzb-li-num{
    float: right;
    color:#00CEFA;
    /*font-weight: 600;*/
}
.typeStyle{
    color:#1079A6;
    font-weight: 600;
}
.font-w{
    color: #ffffff!important;
}
.yj-span{
    display: block;
    width: 30px;
    float: right;
    text-align: right;
}

.speed-container{
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px solid #052025;
    padding-bottom: 10px;
}
.speed-container .speed-title{
    overflow: hidden;
    line-height: 30px;
}
.speed-container .speed-title span{
    display: block;
    float: left;
    color: #ffffff;
    padding-right: 10px;
}
.speed-content{
    margin-top: 10px;
    width: 100%;
}
.speed-content ul{
    width: 100%;
}
.speed-content ul li{
    width: 100%;
    list-style: none;
    overflow: hidden;
    margin: 0;
}
.speed-content ul li span:nth-child(1){
    display: block;
    /*height: 20px;*/
    float: left;
    padding: 0;
    margin: 0;
    /*line-height: 20px;*/
}
.speed-content ul li span:nth-child(2){
    display: block;
    /*height: 20px;*/
    float: right;
    padding: 0;
    margin: 0;
    /*line-height: 20px;*/
}
.speed-line{
    width: 200px;
    background-color: #043037;
    height: 13px;
    margin-top: 8px!important;
    border-radius: 5px;
    position: relative;
}
.speed-num{
    /*width: 90%;*/
    display: block;
    height: 100%;
    border-radius: 5px;
}

.numText{
    position: absolute;
    top: -9px;
    right: 4px;
    color: #ffffff;
    font-size: 12px;
}

.legend-container{
    height: 48px;
    background-color: rgba(22, 31, 38, 0.87);
    overflow: hidden;
}
.legend-content{
    height: 100%;

}
.legend-content:nth-child(1){
    float: left;
}
.legend-content:nth-child(2){
    float: right;
    font-size: 14px!important;
    overflow: hidden;

}
.legend-content span{
    line-height: 48px;
    color: #FFFFFF;
    font-weight: 600;
    font-size: 16px;

}
.legend-content span img{
    vertical-align: middle;
    margin-top: -4px;
    margin-left: 10px;
    margin-right: 10px;
}

.legend-content:nth-child(2) span{
    float: left;
    display: block;
    margin-right: 10px;
}

.red-legend{
    height: 8px;
    width: 30px;
    background-color: #FF3774;
    border-radius: 5px;
    margin-top: 20px;
}
.green-legend{
    height: 8px;
    width: 30px;
    background-color: #6AE89C;
    border-radius: 5px;
    margin-top: 20px;
}

.tip-dropDown{

}
.tip-dropDown:hover{
    cursor: pointer;
}

.info-container{
    width: 100%;

}
.info-container-title{
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
}
.info-container-people{
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
}
.info-container-speed{
    width: 100%;
}
.info-container-speed-content{
    width: 204px;
    margin-left: 18px;
    background-color: #58618A;
    height: 14px;
    margin-bottom: 10px;
    border-radius: 5px;
}
.info-container-speed-content-inside-1{
    display: block;
    height: 100%;
    border-radius: 5px;
    background-color: #FF3774;
    width: 0%;
}
.info-container-speed-content-inside-2{
    display: block;
    height: 100%;
    border-radius: 5px;
    background-color: #6AE89C;
    width: 0%;
}
.info-container-speed-content-inside-1-num{
    position: absolute;
    top: 54px;
    left: 110px;
}
.info-container-speed-content-inside-2-num{
    position: absolute;
    top: 80px;
    left: 110px;

}
